import decimal
from decimal import Decimal, ROUND_HALF_UP
from django.db import models
from django.utils import timezone
from django.core.exceptions import ObjectDoesNotExist
from django.utils.translation import gettext_lazy as _

class Category(models.Model):
    """
    Category model for organizing items and raw materials.
    """
    name = models.CharField(_('Name'), max_length=100)
    description = models.TextField(_('Description'), blank=True, null=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True,
                           related_name='children', verbose_name=_('Parent Category'))
    is_active = models.<PERSON><PERSON>anField(_('Active'), default=True)
    created_at = models.DateTimeField(_('Created At'), default=timezone.now)
    updated_at = models.DateTimeField(_('Updated At'), default=timezone.now)

    class Meta:
        verbose_name = _('Category')
        verbose_name_plural = _('Categories')

    def __str__(self):
        return self.name

class Currency(models.Model):
    code = models.CharField(_('Code'), max_length=3, unique=True)
    name = models.CharField(_('Name'), max_length=50)
    symbol = models.CharField(_('Symbol'), max_length=10)

    class Meta:
        verbose_name = _('Currency')
        verbose_name_plural = _('Currencies')

    def __str__(self):
        return self.code

class ExchangeRate(models.Model):
    from_currency = models.ForeignKey(
        Currency, on_delete=models.CASCADE, related_name='from_exchange_rates'
    )
    to_currency = models.ForeignKey(
        Currency, on_delete=models.CASCADE, related_name='to_exchange_rates'
    )
    rate = models.DecimalField(max_digits=20, decimal_places=4)
    date = models.DateField(default=timezone.now)

    @classmethod
    def get_exchange_rate(cls, from_currency_code, to_currency_code, date=None):
        # If the currencies are the same, return 1.0 (no conversion needed)
        if from_currency_code == to_currency_code:
            return Decimal('1.0')

        if date is None:
            date = timezone.now().date()

        try:
            # Try to get the exchange rate directly
            exchange_rate_obj = (
                cls.objects.filter(
                    from_currency__code=from_currency_code,
                    to_currency__code=to_currency_code,
                    date__lte=date
                )
                .order_by('-date')
                .first()
            )

            if exchange_rate_obj:
                return exchange_rate_obj.rate

            # If not found, try the inverse rate and calculate 1/rate
            inverse_rate_obj = (
                cls.objects.filter(
                    from_currency__code=to_currency_code,
                    to_currency__code=from_currency_code,
                    date__lte=date
                )
                .order_by('-date')
                .first()
            )

            if inverse_rate_obj:
                # Return 1/rate for the inverse conversion
                return Decimal('1.0') / inverse_rate_obj.rate

            # If still not found, return default 1.0
            print(f"No exchange rate found for {from_currency_code} to {to_currency_code}. Using default rate 1.0")
            return Decimal('1.0')

        except Exception as e:
            print(f"Error getting exchange rate: {str(e)}")
            return Decimal('1.0')

    def __str__(self):
        return f'{self.from_currency.code} to {self.to_currency.code} at {self.rate} on {self.date}'

class Unit(models.Model):
    name = models.CharField(max_length=50)
    abbreviation = models.CharField(max_length=10)
    ratio = models.DecimalField(max_digits=10, decimal_places=5)

    def __str__(self):
        return self.name

class Supplier(models.Model):
    name = models.CharField(max_length=255)
    phone = models.CharField(max_length=20, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

class RawMaterial(models.Model):
    """
    Raw material model representing materials used to create items.
    """
    name = models.CharField(_('Name'), max_length=255)
    description = models.TextField(_('Description'), blank=True, null=True)
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE, verbose_name=_('Unit'))
    suppliers = models.ManyToManyField(
        Supplier, through='SupplierRawMaterial', related_name='raw_materials',
        verbose_name=_('Suppliers')
    )
    price_per_unit = models.DecimalField(_('Price Per Unit'), max_digits=10, decimal_places=2)
    currency = models.ForeignKey(Currency, on_delete=models.CASCADE, verbose_name=_('Currency'))
    is_active = models.BooleanField(_('Active'), default=True)
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True,
                               related_name='raw_materials', verbose_name=_('Category'))
    reorder_point = models.DecimalField(_('Reorder Point'), max_digits=10, decimal_places=2, default=0,
                                     help_text=_('Minimum quantity before reordering'))
    created_at = models.DateTimeField(_('Created At'), default=timezone.now)
    updated_at = models.DateTimeField(_('Updated At'), default=timezone.now)

    class Meta:
        verbose_name = _('Raw Material')
        verbose_name_plural = _('Raw Materials')

    def __str__(self):
        return self.name

    def get_current_stock(self):
        """
        Get the current stock level of this raw material.
        """
        try:
            inventory = self.rawmaterialinventory_set.first()
            return inventory.quantity if inventory else Decimal('0')
        except Exception:
            return Decimal('0')

    def needs_reorder(self):
        """
        Check if the raw material needs to be reordered.
        """
        return self.get_current_stock() <= self.reorder_point

class SupplierRawMaterial(models.Model):
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE)
    raw_material = models.ForeignKey(RawMaterial, on_delete=models.CASCADE)
    price_per_unit = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.ForeignKey(Currency, on_delete=models.CASCADE)
    delivery_time_days = models.IntegerField(default=0)
    minimum_order_quantity = models.DecimalField(max_digits=10, decimal_places=2, default=1)

    def __str__(self):
        return f"{self.raw_material.name} from {self.supplier.name} at {self.price_per_unit} {self.currency.code}"

class Item(models.Model):
    """
    Item model representing products that can be created from raw materials.
    """
    name = models.CharField(_('Name'), max_length=100)
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE, verbose_name=_('Unit'))
    description = models.TextField(_('Description'), blank=True, null=True)
    selling_price = models.DecimalField(_('Selling Price'), max_digits=10, decimal_places=2)
    currency = models.ForeignKey(Currency, on_delete=models.CASCADE, verbose_name=_('Currency'))
    raw_materials = models.ManyToManyField(RawMaterial, through='ItemRawMaterial', verbose_name=_('Raw Materials'))
    minimum_stock_level = models.IntegerField(_('Minimum Stock Level'), default=0,
                                     help_text=_('Not applicable for made-to-order items'))
    maximum_stock_level = models.IntegerField(_('Maximum Stock Level'), default=9999,
                                     help_text=_('Not applicable for made-to-order items'))
    stock_quantity = models.IntegerField(_('Stock Quantity'), default=0)
    is_active = models.BooleanField(_('Active'), default=True)
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True,
                               related_name='items', verbose_name=_('Category'))
    created_at = models.DateTimeField(_('Created At'), default=timezone.now)
    updated_at = models.DateTimeField(_('Updated At'), default=timezone.now)

    class Meta:
        verbose_name = _('Item')
        verbose_name_plural = _('Items')

    def __str__(self):
        return self.name

    def cost(self):
        """
        Calculate the total cost of the item based on its raw materials.
        Uses IFRS/SKK compliant inventory costing methods.
        Returns the cost in the item's currency.
        """
        total_cost = Decimal('0')
        raw_materials = self.itemrawmaterial_set.all()

        if not raw_materials.exists():
            return Decimal('0.00')

        # Import here to avoid circular imports
        from items.services.inventory_costing import InventoryCostingService
        costing_service = InventoryCostingService()

        for irm in raw_materials:
            # Use actual inventory cost instead of base price (IFRS/SKK compliant)
            current_unit_cost = costing_service.get_current_material_cost(irm.raw_material)
            cost_in_raw_currency = current_unit_cost * irm.quantity

            # Apply currency conversion if needed
            if irm.raw_material.currency and irm.raw_material.currency.code != self.currency.code:
                exchange_rate = ExchangeRate.get_exchange_rate(
                    irm.raw_material.currency.code, self.currency.code
                )
                cost_in_item_currency = cost_in_raw_currency * exchange_rate
            else:
                cost_in_item_currency = cost_in_raw_currency

            total_cost += cost_in_item_currency

        return total_cost.quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)

    def cost_breakdown(self):
        """
        Get detailed cost breakdown for this item (useful for analysis)
        Returns list of dictionaries with material cost details
        """
        breakdown = []
        raw_materials = self.itemrawmaterial_set.all()

        if not raw_materials.exists():
            return breakdown

        from items.services.inventory_costing import InventoryCostingService
        costing_service = InventoryCostingService()

        for irm in raw_materials:
            current_unit_cost = costing_service.get_current_material_cost(irm.raw_material)
            cost_in_raw_currency = current_unit_cost * irm.quantity

            # Get inventory info for additional details
            try:
                inventory = RawMaterialInventory.objects.get(raw_material=irm.raw_material)
                costing_method = inventory.get_costing_method_display()
            except RawMaterialInventory.DoesNotExist:
                costing_method = "Base Price (No Inventory)"

            breakdown.append({
                'material_name': irm.raw_material.name,
                'quantity': irm.quantity,
                'unit': irm.unit.name,
                'unit_cost': current_unit_cost,
                'total_cost': cost_in_raw_currency,
                'currency': irm.raw_material.currency.code if irm.raw_material.currency else 'N/A',
                'costing_method': costing_method
            })

        return breakdown

    def profit(self):
        """
        Calculate the profit margin of the item (selling price - cost).
        Returns the profit in the item's currency.
        """
        cost = self.cost()
        profit = self.selling_price - cost
        return profit.quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)

    def profit_percentage(self):
        """
        Calculate the profit percentage of the item.
        Returns the profit percentage as a decimal (e.g., 0.25 for 25%).
        """
        cost = self.cost()
        if cost <= 0:
            return Decimal('0.00')
        profit = self.profit()
        percentage = (profit / cost) * 100
        return percentage.quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)

class ItemRawMaterial(models.Model):
    item = models.ForeignKey(Item, on_delete=models.CASCADE)
    raw_material = models.ForeignKey(RawMaterial, on_delete=models.CASCADE)
    quantity = models.DecimalField(max_digits=10, decimal_places=2)
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE)

    class Meta:
        unique_together = ('item', 'raw_material')

    def __str__(self):
        return f"{self.item.name} - {self.raw_material.name}: {self.quantity} {self.unit.name}"

    def quantity_in_item_unit(self):
        return self.quantity * (self.unit.ratio / self.item.unit.ratio)

class Inventory(models.Model):
    item = models.ForeignKey(Item, on_delete=models.CASCADE)
    quantity = models.DecimalField(max_digits=10, decimal_places=2)
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE)
    location = models.CharField(max_length=255, blank=True, null=True)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.item.name} - {self.quantity} {self.unit.name}"

class RawMaterialInventory(models.Model):
    COSTING_METHOD_CHOICES = [
        ('WAC', 'Weighted Average Cost'),
        ('SPECIFIC', 'Specific Identification'),
        ('FIFO', 'First In First Out')
    ]

    raw_material = models.ForeignKey(RawMaterial, on_delete=models.CASCADE)
    quantity = models.DecimalField(max_digits=10, decimal_places=2)
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE)
    location = models.CharField(max_length=255, blank=True, null=True)
    last_updated = models.DateTimeField(auto_now=True)

    # IFRS/SKK Compliant Costing Fields
    costing_method = models.CharField(
        max_length=20,
        choices=COSTING_METHOD_CHOICES,
        default='WAC',
        help_text="IFRS/SKK compliant costing method"
    )
    weighted_average_cost = models.DecimalField(
        max_digits=10, decimal_places=4, default=0,
        help_text="Current weighted average cost per unit"
    )
    total_cost_basis = models.DecimalField(
        max_digits=15, decimal_places=2, default=0,
        help_text="Total cost basis of current inventory"
    )
    currency = models.ForeignKey(
        Currency, on_delete=models.CASCADE, null=True, blank=True,
        help_text="Currency for cost calculations"
    )
    last_cost_update = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.raw_material.name} - {self.quantity} {self.unit.name}"

    def get_current_unit_cost(self):
        """Get current unit cost based on costing method"""
        if self.costing_method == 'WAC':
            return self.weighted_average_cost
        elif self.costing_method == 'FIFO':
            return self._get_fifo_unit_cost()
        elif self.costing_method == 'SPECIFIC':
            return self._get_specific_unit_cost()
        return self.raw_material.price_per_unit  # Fallback

    def get_inventory_value(self):
        """Get total inventory value using current costing method"""
        return self.quantity * self.get_current_unit_cost()

    def _get_fifo_unit_cost(self):
        """Get unit cost for FIFO method (oldest batch)"""
        oldest_batch = self.rawmaterialinventorybatch_set.filter(
            quantity_remaining__gt=0,
            is_depleted=False
        ).order_by('purchase_date').first()

        if oldest_batch:
            return oldest_batch.unit_cost
        return self.raw_material.price_per_unit

    def _get_specific_unit_cost(self):
        """Get unit cost for specific identification method"""
        # For specific identification, use the same logic as FIFO for now
        return self._get_fifo_unit_cost()

class RawMaterialInventoryHistory(models.Model):
    RAW_MATERIAL_INVENTORY_CHANGE_TYPES = (
        ('purchase_order', 'Purchase Order'),
        ('case', 'Case'),
    )
    raw_material_inventory = models.ForeignKey(RawMaterialInventory, on_delete=models.CASCADE)
    change_type = models.CharField(max_length=20, choices=RAW_MATERIAL_INVENTORY_CHANGE_TYPES)
    change_quantity = models.DecimalField(max_digits=10, decimal_places=2)
    purchase_order_item = models.ForeignKey(
        'billing.PurchaseOrderItem', on_delete=models.SET_NULL, null=True, blank=True
    )
    case_item = models.ForeignKey(
        'case.CaseItem', on_delete=models.SET_NULL, null=True, blank=True
    )
    timestamp = models.DateTimeField(auto_now_add=True)

    # IFRS/SKK Compliant Costing Fields
    unit_cost = models.DecimalField(
        max_digits=10, decimal_places=4, default=0,
        help_text="Cost per unit for this transaction"
    )
    total_cost = models.DecimalField(
        max_digits=15, decimal_places=2, default=0,
        help_text="Total cost for this transaction"
    )
    currency = models.ForeignKey(
        Currency, on_delete=models.CASCADE, null=True, blank=True
    )
    new_weighted_average_cost = models.DecimalField(
        max_digits=10, decimal_places=4, null=True, blank=True,
        help_text="New WAC after this transaction (for WAC method)"
    )
    batch_reference = models.CharField(
        max_length=100, blank=True,
        help_text="Batch reference for FIFO/Specific identification"
    )

    def __str__(self):
        return f"{self.raw_material_inventory} - {self.change_type} - {self.change_quantity}"


class RawMaterialInventoryBatch(models.Model):
    """
    Track individual batches for FIFO and Specific Identification methods
    Required for IFRS/SKK compliance when using these costing methods
    """
    inventory = models.ForeignKey(RawMaterialInventory, on_delete=models.CASCADE)
    batch_number = models.CharField(max_length=50, unique=True)
    purchase_order_item = models.ForeignKey(
        'billing.PurchaseOrderItem', on_delete=models.CASCADE
    )

    quantity_purchased = models.DecimalField(max_digits=10, decimal_places=2)
    quantity_remaining = models.DecimalField(max_digits=10, decimal_places=2)
    unit_cost = models.DecimalField(max_digits=10, decimal_places=4)
    currency = models.ForeignKey(Currency, on_delete=models.CASCADE)

    purchase_date = models.DateTimeField()
    expiry_date = models.DateTimeField(null=True, blank=True)

    is_depleted = models.BooleanField(default=False)

    class Meta:
        ordering = ['purchase_date']  # For FIFO ordering
        verbose_name = "Raw Material Inventory Batch"
        verbose_name_plural = "Raw Material Inventory Batches"

    def __str__(self):
        return f"Batch {self.batch_number} - {self.inventory.raw_material.name}"

    def is_available(self):
        """Check if batch has remaining quantity"""
        return self.quantity_remaining > 0 and not self.is_depleted

    def consume_quantity(self, quantity):
        """Consume quantity from this batch"""
        if quantity > self.quantity_remaining:
            raise ValueError(f"Cannot consume {quantity}, only {self.quantity_remaining} remaining")

        self.quantity_remaining -= quantity
        if self.quantity_remaining <= 0:
            self.is_depleted = True
        self.save()
