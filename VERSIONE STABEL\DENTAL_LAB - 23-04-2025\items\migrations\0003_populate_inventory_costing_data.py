# Data migration to populate initial costing data

from django.db import migrations, models
from decimal import Decimal


def populate_inventory_costing_data(apps, schema_editor):
    """Populate initial costing data for existing inventory"""
    RawMaterialInventory = apps.get_model('items', 'RawMaterialInventory')
    RawMaterialInventoryHistory = apps.get_model('items', 'RawMaterialInventoryHistory')
    RawMaterial = apps.get_model('items', 'RawMaterial')
    Currency = apps.get_model('items', 'Currency')

    # Get USD currency as default (or create if doesn't exist)
    usd_currency, created = Currency.objects.get_or_create(
        code='USD',
        defaults={'name': 'US Dollar', 'symbol': '$'}
    )

    # Update all existing RawMaterialInventory records
    for inventory in RawMaterialInventory.objects.all():
        # Set currency to raw material's currency or USD as fallback
        if hasattr(inventory.raw_material, 'currency') and inventory.raw_material.currency:
            inventory.currency = inventory.raw_material.currency
        else:
            inventory.currency = usd_currency

        # Determine costing method based on material characteristics
        if inventory.raw_material.price_per_unit >= 100:  # Expensive materials
            inventory.costing_method = 'SPECIFIC'
        else:
            inventory.costing_method = 'WAC'  # Default to Weighted Average Cost

        # Set initial weighted average cost to current base price
        inventory.weighted_average_cost = inventory.raw_material.price_per_unit

        # Calculate initial total cost basis
        inventory.total_cost_basis = inventory.quantity * inventory.weighted_average_cost

        inventory.save()

    # Update existing history records
    for history in RawMaterialInventoryHistory.objects.all():
        # Set currency from related inventory or raw material
        if history.raw_material_inventory.currency:
            history.currency = history.raw_material_inventory.currency
        else:
            history.currency = usd_currency

        # Set unit cost based on purchase order item or base price
        if history.purchase_order_item:
            history.unit_cost = history.purchase_order_item.price_per_unit
            history.total_cost = history.change_quantity * history.unit_cost
        else:
            # For case consumption or other changes, use base price
            history.unit_cost = history.raw_material_inventory.raw_material.price_per_unit
            history.total_cost = abs(history.change_quantity) * history.unit_cost

        # Set new weighted average cost (for WAC method)
        if history.raw_material_inventory.costing_method == 'WAC':
            history.new_weighted_average_cost = history.raw_material_inventory.weighted_average_cost

        history.save()


def reverse_populate_inventory_costing_data(apps, schema_editor):
    """Reverse the data population (no-op since we're just setting defaults)"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('items', '0002_add_inventory_costing_fields'),
        ('billing', '0001_initial'),  # Ensure billing app is migrated
    ]

    operations = [
        migrations.RunPython(
            populate_inventory_costing_data,
            reverse_populate_inventory_costing_data
        ),

        # Make currency field required after populating data
        migrations.AlterField(
            model_name='rawmaterialinventory',
            name='currency',
            field=models.ForeignKey(
                help_text='Currency for cost calculations',
                on_delete=django.db.models.deletion.CASCADE,
                to='items.currency'
            ),
        ),
        migrations.AlterField(
            model_name='rawmaterialinventoryhistory',
            name='currency',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to='items.currency'
            ),
        ),
    ]
