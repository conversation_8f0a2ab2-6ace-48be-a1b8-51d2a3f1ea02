"""
IFRS/SKK Compliant Inventory Costing Service

This service implements the following costing methods:
- Weighted Average Cost (WAC) - for common materials
- FIFO (First In, First Out) - for materials with expiry dates
- Specific Identification - for expensive/unique materials

Complies with:
- IFRS IAS 2 - Inventories
- SKK 2 - Inventarët (Albania)
"""

import logging
from decimal import Decimal, ROUND_HALF_UP
from django.utils import timezone
from django.db import transaction
from django.core.exceptions import ValidationError

logger = logging.getLogger(__name__)


class InventoryCostingService:
    """IFRS/SKK compliant inventory costing service"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    @transaction.atomic
    def update_inventory_on_purchase(self, purchase_order_item):
        """
        Update inventory when materials are purchased
        
        Args:
            purchase_order_item: PurchaseOrderItem instance
        """
        try:
            from items.models import RawMaterialInventory
            
            self.logger.info(f"Updating inventory for purchase: {purchase_order_item}")
            
            # Get or create inventory record
            inventory, created = RawMaterialInventory.objects.get_or_create(
                raw_material=purchase_order_item.raw_material,
                unit=purchase_order_item.unit,
                defaults={
                    'quantity': Decimal('0'),
                    'costing_method': self._determine_costing_method(purchase_order_item.raw_material),
                    'weighted_average_cost': Decimal('0'),
                    'total_cost_basis': Decimal('0'),
                    'currency': purchase_order_item.currency,
                }
            )
            
            # Update inventory based on costing method
            if inventory.costing_method == 'WAC':
                self._update_weighted_average_cost(inventory, purchase_order_item)
            elif inventory.costing_method == 'FIFO':
                self._create_fifo_batch(inventory, purchase_order_item)
            elif inventory.costing_method == 'SPECIFIC':
                self._create_specific_batch(inventory, purchase_order_item)
            
            # Create history record
            self._create_purchase_history_record(inventory, purchase_order_item)
            
            self.logger.info(f"Successfully updated inventory for {purchase_order_item.raw_material.name}")
            
        except Exception as e:
            self.logger.error(f"Error updating inventory on purchase: {str(e)}")
            raise
    
    def _determine_costing_method(self, raw_material):
        """
        Determine appropriate costing method based on material characteristics
        
        Args:
            raw_material: RawMaterial instance
            
        Returns:
            str: Costing method ('WAC', 'FIFO', 'SPECIFIC')
        """
        # Expensive materials (>= €100) use Specific Identification
        if raw_material.price_per_unit >= 100:
            return 'SPECIFIC'
        
        # Materials with expiry concerns could use FIFO
        # For now, default to WAC for common materials
        return 'WAC'
    
    def _update_weighted_average_cost(self, inventory, purchase_item):
        """
        Update inventory using Weighted Average Cost method
        
        Args:
            inventory: RawMaterialInventory instance
            purchase_item: PurchaseOrderItem instance
        """
        # Convert purchase cost to inventory currency if needed
        purchase_unit_cost = self._convert_currency(
            purchase_item.price_per_unit,
            purchase_item.currency,
            inventory.currency
        )
        
        # Current inventory value
        current_value = inventory.quantity * inventory.weighted_average_cost
        
        # New purchase value
        new_value = purchase_item.quantity * purchase_unit_cost
        
        # Calculate new weighted average cost
        total_value = current_value + new_value
        total_quantity = inventory.quantity + purchase_item.quantity
        
        if total_quantity > 0:
            new_wac = total_value / total_quantity
            inventory.weighted_average_cost = new_wac.quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
            inventory.total_cost_basis = total_value.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            inventory.quantity = total_quantity
            inventory.save()
            
            self.logger.info(f"Updated WAC for {inventory.raw_material.name}: {inventory.weighted_average_cost}")
    
    def _create_fifo_batch(self, inventory, purchase_item):
        """
        Create FIFO batch for First In, First Out costing
        
        Args:
            inventory: RawMaterialInventory instance
            purchase_item: PurchaseOrderItem instance
        """
        from items.models import RawMaterialInventoryBatch
        
        # Generate unique batch number
        batch_number = f"BATCH-{purchase_item.purchase_order.id}-{purchase_item.id}-{timezone.now().strftime('%Y%m%d%H%M%S')}"
        
        # Convert cost to inventory currency
        unit_cost = self._convert_currency(
            purchase_item.price_per_unit,
            purchase_item.currency,
            inventory.currency
        )
        
        # Create batch record
        batch = RawMaterialInventoryBatch.objects.create(
            inventory=inventory,
            batch_number=batch_number,
            purchase_order_item=purchase_item,
            quantity_purchased=purchase_item.quantity,
            quantity_remaining=purchase_item.quantity,
            unit_cost=unit_cost,
            currency=inventory.currency,
            purchase_date=timezone.now()
        )
        
        # Update inventory totals
        inventory.quantity += purchase_item.quantity
        inventory.total_cost_basis += purchase_item.quantity * unit_cost
        inventory.save()
        
        self.logger.info(f"Created FIFO batch {batch_number} for {inventory.raw_material.name}")
    
    def _create_specific_batch(self, inventory, purchase_item):
        """
        Create specific identification batch for unique/expensive materials
        
        Args:
            inventory: RawMaterialInventory instance
            purchase_item: PurchaseOrderItem instance
        """
        # For specific identification, each purchase is tracked separately
        # This is similar to FIFO but with more detailed tracking
        self._create_fifo_batch(inventory, purchase_item)
    
    def _convert_currency(self, amount, from_currency, to_currency):
        """
        Convert amount from one currency to another
        
        Args:
            amount: Decimal amount to convert
            from_currency: Currency instance (source)
            to_currency: Currency instance (target)
            
        Returns:
            Decimal: Converted amount
        """
        if from_currency.code == to_currency.code:
            return amount
        
        try:
            from items.models import ExchangeRate
            rate = ExchangeRate.get_exchange_rate(from_currency.code, to_currency.code)
            return (amount * rate).quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
        except Exception as e:
            self.logger.warning(f"Currency conversion failed: {e}. Using 1:1 rate.")
            return amount
    
    def _create_purchase_history_record(self, inventory, purchase_item):
        """
        Create history record for purchase transaction
        
        Args:
            inventory: RawMaterialInventory instance
            purchase_item: PurchaseOrderItem instance
        """
        from items.models import RawMaterialInventoryHistory
        
        unit_cost = self._convert_currency(
            purchase_item.price_per_unit,
            purchase_item.currency,
            inventory.currency
        )
        
        RawMaterialInventoryHistory.objects.create(
            raw_material_inventory=inventory,
            change_type='purchase',
            change_quantity=purchase_item.quantity,
            purchase_order_item=purchase_item,
            unit_cost=unit_cost,
            total_cost=purchase_item.quantity * unit_cost,
            currency=inventory.currency,
            new_weighted_average_cost=inventory.weighted_average_cost if inventory.costing_method == 'WAC' else None,
            batch_reference=f"PO-{purchase_item.purchase_order.id}-{purchase_item.id}"
        )
    
    def get_current_material_cost(self, raw_material):
        """
        Get current unit cost for a raw material based on its costing method
        
        Args:
            raw_material: RawMaterial instance
            
        Returns:
            Decimal: Current unit cost
        """
        try:
            from items.models import RawMaterialInventory
            
            inventory = RawMaterialInventory.objects.get(raw_material=raw_material)
            
            if inventory.costing_method == 'WAC':
                return inventory.weighted_average_cost
            elif inventory.costing_method == 'FIFO':
                return self._get_fifo_unit_cost(inventory)
            elif inventory.costing_method == 'SPECIFIC':
                return self._get_specific_unit_cost(inventory)
            
        except RawMaterialInventory.DoesNotExist:
            # Fallback to base price if no inventory record
            self.logger.warning(f"No inventory record for {raw_material.name}, using base price")
            return raw_material.price_per_unit
        
        return raw_material.price_per_unit
    
    def _get_fifo_unit_cost(self, inventory):
        """Get unit cost for FIFO method (oldest batch)"""
        from items.models import RawMaterialInventoryBatch
        
        oldest_batch = RawMaterialInventoryBatch.objects.filter(
            inventory=inventory,
            quantity_remaining__gt=0,
            is_depleted=False
        ).order_by('purchase_date').first()
        
        if oldest_batch:
            return oldest_batch.unit_cost
        
        return inventory.raw_material.price_per_unit
    
    def _get_specific_unit_cost(self, inventory):
        """Get unit cost for specific identification method"""
        # For specific identification, we might need additional logic
        # to determine which specific batch to use
        # For now, use the same logic as FIFO
        return self._get_fifo_unit_cost(inventory)
    
    def calculate_inventory_value(self, raw_material=None):
        """
        Calculate total inventory value for a material or all materials
        
        Args:
            raw_material: RawMaterial instance (optional)
            
        Returns:
            Decimal: Total inventory value
        """
        from items.models import RawMaterialInventory
        
        if raw_material:
            inventories = RawMaterialInventory.objects.filter(raw_material=raw_material)
        else:
            inventories = RawMaterialInventory.objects.all()
        
        total_value = Decimal('0')
        
        for inventory in inventories:
            if inventory.costing_method == 'WAC':
                value = inventory.quantity * inventory.weighted_average_cost
            else:
                # For FIFO/Specific, sum up batch values
                value = inventory.total_cost_basis
            
            total_value += value
        
        return total_value.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
